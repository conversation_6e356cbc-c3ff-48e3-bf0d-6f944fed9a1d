import pandas as pd

df = pd.read_csv("data/ottawa_real_estate.csv")

# Remove records where property_type equals the string 'NA' (and exclude missing)

# numbers = [1, 2, 3, 4, 5, 6]
# result = [num for num in numbers if num % 2 == 0]

# df_clean = [row for row in df if row == 'property_type' or row != "NA"]
df = df[df["property_type"].notna() & (df["property_type"].astype(str).str.strip() != "NA") & (df["postal_code"].astype(str).str.strip() != "nan")].copy()



# df_clean2.info()          # dtypes & missing counts
# df_clean2.describe()      # numeric stats
# df_clean2.head()
# df_clean2.tail()
# print(df)


# num_cols = df.select_dtypes(include="number").columns
# df[num_cols] = df[num_cols].fillna(df[num_cols].median())
#
# # 3.2  Fix missing categoricals
# cat_cols = df.select_dtypes(include="object").columns
# df[cat_cols] = df[cat_cols].fillna("Missing")
#
# # 3.3  (Optional) log-transform skewed target
# import numpy as np
# df["SalePrice_log"] = np.log1p(df["SalePrice"])
# TARGET = "SalePrice_log"             # set to "SalePrice" if you skip log




df = df.drop(columns=["address", "listing_url", "latitude", "longitude", "mls_id"])

# 3.1  Fix missing numeric values
num_cols = df.select_dtypes(include="number").columns
df[num_cols] = df[num_cols].fillna(df[num_cols].median())

# 3.2  Fix missing categoricals
cat_cols = df.select_dtypes(include="object").columns
df[cat_cols] = df[cat_cols].fillna("Missing")

# 3.3  (Optional) log-transform skewed target
import numpy as np
# df["price_log"] = np.log1p(df["price"])
TARGET = "price"


# 4 Train / test split

from sklearn.model_selection import train_test_split
X = df.drop(columns=[TARGET])
y = df[TARGET]

X_train, X_valid, y_train, y_valid = train_test_split(
    X, y, test_size=0.2, random_state=42)

# 5 Identify categorical feature indices
cat_features = [X.columns.get_loc(c) for c in cat_cols]


# 6 Fit a CatBoostRegressor
from catboost import CatBoostRegressor, Pool

train_pool = Pool(X_train, y_train, cat_features=cat_features)
valid_pool = Pool(X_valid, y_valid, cat_features=cat_features)

model = CatBoostRegressor(
    depth=6,
    learning_rate=0.05,
    iterations=1000,            # stop early if no gain
    loss_function="RMSE",
    random_seed=42,
    verbose=100                 # prints every 100 trees
)

model.fit(train_pool, eval_set=valid_pool, use_best_model=True)

# 7 Evaluate
from sklearn.metrics import mean_squared_error, r2_score
import numpy as np
import math

pred_valid = model.predict(X_valid)
# rmse = mean_squared_error(y_valid, pred_valid, squared=False)
rmse = math.sqrt(mean_squared_error(y_valid, pred_valid))
r2   = r2_score(y_valid, pred_valid)
print(f"Validation RMSE: {rmse:.3f}")
print(f"Validation R²  : {r2:.3f}")

# if you logged the target, convert RMSE back to dollars
if TARGET.endswith("_log"):
    rmse_dollars = np.expm1(rmse)
    print(f"≈ ${rmse_dollars:,.0f} average error")


# 8 Plot actual vs predicted
import matplotlib.pyplot as plt
plt.figure(figsize=(6,6))
plt.scatter(y_valid, pred_valid, alpha=0.3)
plt.plot([y_valid.min(), y_valid.max()],
         [y_valid.min(), y_valid.max()], "k--", lw=2)
plt.xlabel("Actual log-price"); plt.ylabel("Predicted log-price")
plt.title("CatBoost – Actual vs Predicted")
plt.tight_layout(); plt.show()


model.save_model("real-estate-model.json", format="json")

