from catboost import CatBoostRegressor
import pandas as pd

loaded_model = CatBoostRegressor()
loaded_model.load_model("real-estate-model.json", format="json")

# address,price,beds,baths,sqft,property_type,latitude,longitude,mls_id,postal_code,listing_url
new_df = pd.DataFrame(
    {
        "beds":   [5, 3, 3],
        "baths": [3, 3, 2],
        "sqft":   [1750, 1300, 1300],
        "postal_code": ["K4K 0J1", "K2G 5G5", "K1C 2X9"],
        "property_type": ["House", "Townhouse", "House"],
    }
)
# for col in num_cols:
#     new_df[col] = new_df[col].fillna(median_values[col])

# categorical missing token
# for col in cat_cols:
#     new_df[col] = new_df[col].fillna("Missing")


# use it
log_preds = loaded_model.predict(new_df)

print(log_preds)
