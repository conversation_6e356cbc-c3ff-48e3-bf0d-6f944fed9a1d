#!/usr/bin/env python3
"""
Geocoding script to add postal codes to Ottawa real estate data using OpenStreetMap Nominatim.
This script processes the ottawa_real_estate.csv file and adds postal codes for addresses
that don't already have them.
"""

import pandas as pd
import requests
import time
import sys
import json
from urllib.parse import quote_plus
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('geocoding.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class NominatimGeocoder:
    """
    Geocoder class for OpenStreetMap Nominatim API
    """
    
    def __init__(self, rate_limit_seconds=2.0):
        self.base_url = "https://nominatim.openstreetmap.org/search"
        self.rate_limit_seconds = rate_limit_seconds
        self.user_agent = "Ottawa Real Estate Geocoder/1.0"
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0"
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': self.user_agent})
        self.last_request_time = 0
        
    def _rate_limit(self):
        """Ensure we don't exceed rate limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_seconds:
            sleep_time = self.rate_limit_seconds - time_since_last
            logger.info(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            if (sleep_time < 2):
                sleep_time = 2

            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def geocode_address(self, address):
        """
        Geocode a single address and return postal code
        
        Args:
            address (str): The address to geocode
            
        Returns:
            dict: Result containing postal_code and status information
        """
        try:
            # Apply rate limiting
            self._rate_limit()
            
            # Clean and format address for URL
            # Remove extra spaces and format for URL encoding
            clean_address = ' '.join(address.split())
            encoded_address = quote_plus(clean_address)
            
            # Build request URL
            params = {
                'addressdetails': '1',
                'q': clean_address,
                'format': 'jsonv2',
                'limit': '1'
            }
            
            logger.info(f"Geocoding: {address}")
            
            # Make request
            response = self.session.get(self.base_url, params=params, timeout=10)
            
            # Check for rate limiting or blocking
            if response.status_code == 429:
                logger.error("Rate limited by server (429). Stopping to avoid being blocked.")
                return {
                    'postal_code': 'NA',
                    'success': False,
                    'error': 'rate_limited',
                    'stop_processing': True
                }
            
            if response.status_code == 403:
                logger.error("Blocked by server (403). Stopping.")
                return {
                    'postal_code': 'NA',
                    'success': False,
                    'error': 'blocked',
                    'stop_processing': True
                }
            
            response.raise_for_status()
            data = response.json()
            
            if data and len(data) > 0:
                result = data[0]
                address_info = result.get('address', {})
                postal_code = address_info.get('postcode', 'NA')
                
                if postal_code and postal_code != 'NA':
                    logger.info(f"Found postal code: {postal_code}")
                    return {
                        'postal_code': postal_code,
                        'success': True,
                        'error': None,
                        'stop_processing': False
                    }
                else:
                    logger.warning(f"No postal code found in response")
                    return {
                        'postal_code': 'NA',
                        'success': False,
                        'error': 'no_postal_code',
                        'stop_processing': False
                    }
            else:
                logger.warning(f"No results found for address")
                return {
                    'postal_code': 'NA',
                    'success': False,
                    'error': 'no_results',
                    'stop_processing': False
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error: {e}")
            return {
                'postal_code': 'NA',
                'success': False,
                'error': f'request_error: {str(e)}',
                'stop_processing': False
            }
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return {
                'postal_code': 'NA',
                'success': False,
                'error': f'unexpected_error: {str(e)}',
                'stop_processing': False
            }

def save_progress(df, filename):
    """Save current progress to file"""
    try:
        df.to_csv(filename, index=False)
        logger.info(f"Progress saved to {filename}")
        return True
    except Exception as e:
        logger.error(f"Failed to save progress: {e}")
        return False

def main():
    """Main function to process the CSV file"""
    input_file = 'data/ottawa_real_estate.csv'

    try:
        # Load the CSV file
        logger.info(f"Loading {input_file}")
        df = pd.read_csv(input_file)

        # Ensure postal_code column is string type to avoid pandas warnings
        df['postal_code'] = df['postal_code'].astype(str)


        # Initialize geocoder
        geocoder = NominatimGeocoder(rate_limit_seconds=2.0)
        
        # Track progress
        total_rows = len(df)
        processed_count = 0
        success_count = 0
        
        logger.info(f"Starting geocoding for {total_rows} addresses")

        # For testing, limit to first 3 rows
        # Remove these lines for full processing
        # df = df.head(3)
        # total_rows = len(df)

        # Process each row
        for index, row in df.iterrows():
            current_postal = str(row['postal_code']).strip()

            # Skip if postal code already exists and is not 'NA' or 'nan'
            if (current_postal and
                current_postal.upper() not in ['NA', 'NAN'] and
                pd.notna(row['postal_code'])):
                logger.info(f"Row {index + 1}: Skipping - postal code already exists: {current_postal}")
                processed_count += 1
                continue
            
            address = row['address']
            logger.info(f"Row {index + 1}/{total_rows}: Processing {address}")
            
            # Geocode the address
            result = geocoder.geocode_address(address)
            
            # Check if we should stop processing
            if result.get('stop_processing', False):
                logger.error("Stopping processing due to rate limiting or blocking")
                break
            
            # Update the dataframe
            df.at[index, 'postal_code'] = result['postal_code']
            
            if result['success']:
                success_count += 1
            
            processed_count += 1
            
            # Save progress every 10 rows
            if processed_count % 10 == 0:
                save_progress(df, input_file)
                logger.info(f"Progress: {processed_count}/{total_rows} processed, {success_count} successful")
        
        # Final save
        save_progress(df, input_file)
        
        logger.info(f"Geocoding completed!")
        logger.info(f"Total processed: {processed_count}/{total_rows}")
        logger.info(f"Successful geocodes: {success_count}")
        logger.info(f"Results saved to {input_file}")
        
    except FileNotFoundError:
        logger.error(f"File {input_file} not found")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
